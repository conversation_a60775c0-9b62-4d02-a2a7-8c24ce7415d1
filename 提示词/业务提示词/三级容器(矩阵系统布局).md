# 三级容器(矩阵系统布局)

**1.客户端组件:**

- 1.渲染指令:'use client'

## 基于‘compnetA1.tsx’容器布局

**1.组件布局(featureA1_layout.tsx):**

- 1.装载容器:'componetA1'
- 2.存放组件:'featureA1'
- 3.排列方式:网格排列
  - 1.网格行数:33
  - 2.网格列数:33
- 4.自动布局:采用自动布局系统
  - 1.容器宽度:‘componetA1’容器宽度99%
  - 2.容器高度:'componetA1'容器高度99%
  - 3.组件间隔:5px

## 功能组件定义

**1.功能组件1(featureA1.tsx):**

- 1.组件形状:方形
- 2.组件尺寸:自动确定
- 3.组件圆角:5px
- 4.展示方式:弹性布局
- 5.弹性方向:垂直
- 6.对齐方式:水平，垂直居中
- 7.溢出处理:隐藏
- 8.组件定位:相对定位
- 9.鼠标指针:手型

**2.文字样式(featureA1.tsx):**

- 1.默认文本:无
- 2.字体大小:8px
- 3.字体颜色:#242424
- 4.字体对齐:居中

**3.坐标显示(featureA1_coordinate.tsx):**

- 1.访问‘store.ts’中‘坐标按键’的状态('true'/'false')
- 2.状态为‘true’时:
  - 1.计算每个组件的坐标 (以中心组件为原点 '0,0')
  - 2.将坐标字符串(如: '-1,0','1,0')作为文本显示在对应组件内部
- 3.状态为‘false’时:
  - 1.隐藏所有组件坐标文本

**4.组件底色(featureA1_color.tsx):**

- 1.监听‘store.ts’中的‘模式’按键状态('true'/false)
  - 1.状态为‘true’
    - 1.组件底色:#ffffff
  - 2.状态为‘false’
    - 1.组件底色:坐标底色

**5.坐标底色(featureA1_color.tsx):**

- 1.获取组件对应坐标
  - 1.对应坐标显示对应颜色和编号
  - 2.超出33 x 33范围的坐标自动忽略
- 2.定义A组常量对象(group_a)管理颜色坐标
  - 1.颜色坐标
    - 1.黑色:
      - 1.中心编号:'A'
      - 2.中心坐标:'0,0'/ #1E1E1E
    - 2.红色:
      - 1.编号:'1'
      - 2.一级坐标:'8,0'/ #D00000
      - 3.二级坐标:'4,0'/ #D00000
      - 4.三级坐标:'2,0','6,0','4,2','4,-2'/ #FF9190
      - 5.四级坐标:'1,0','3.0','5,0','7,0','2,1','2,-1','3,2','3,-2','4,1','4,3','4,-1','4,-3','6,1','6,-1','5,2','5,-2'/ #FF9190
    - 3.橙色:
      - 1.编号:'2'
      - 2.一级坐标:'4,-4'/ #FD5D00
      - 3.三级坐标:'6,2','2,-2','-2,-6'/ #FFD500
      - 4.四级坐标:‘-3,-5’,'-1,-7','-1,-5','1,-1','1,-3','3,-3','3,-1','5,3','7,1','5,1'/ #FFD500
    - 4.黄色:
      - 1.编号:'3'
      - 2.一级坐标:'0,-8'/ #B4B100
      - 3.二级坐标:'0,-4'/ #B4B100
      - 4.三级坐标:'0,-2','0,-6','2,-4','-2,-4'/ #FFD500
      - 5.四级坐标:'0,-1','-1,-2','0,-3','1,-2','-2,-3','-3,-4','-2,-5','-1,-4','2,-3','1,-4','2,-5','3,-4','0,-5','-1,-6','0,-7','1,-6'/ #FFD500
    - 5.绿色:
      - 1.编号:'4'
      - 2.一级坐标:'-4,-4'/ #009A15
      - 3.二级坐标:'-6,2','-2,-2','2,-6'/ #0FFF35
      - 4.四级坐标:'-5,3','-7,1','-5,1','-1,-1','-3,-1','-3,-3','-1,-3','3,-5','1,-5','1,-7'/ #0FFF35
    - 6.青色:
      - 1.编号:'5'
      - 2.一级坐标:'-8,0'/ #00BDBD
      - 3.二级坐标:'-4,0'/ #00BDBD
      - 4.三级坐标:'-2,0','-6,0','-4,2','-4,-2'/ #90FFFF
      - 5.四级坐标:'-7,0','-5,0','-3,0','-1,0','-6,1','-6,-1','-5,2','-5,-2','-4,1','-4,3','-4,-1','-4,-3','-2,1','-2,-1','-3,2','-3,-2'/ #90FFFF
    - 7.蓝色:
      - 1.编号:'6'
      - 2.一级坐标:'-4,4'/ #0D00FF
      - 3.二级坐标:'2,6','-2,2','-6,-2'/ #0FD7FF
      - 4.四级坐标:'1,7','3,5','1,5','-3,3','-1,3','-1,1','-3,1','-7,-1','-5,-1','-5,-3'/ #0FD7FF
    - 8.紫色:
      - 1.编号:'7'
      - 2.一级坐标:'0,8'/ #7A00FF
      - 3.二级坐标:'0,4'/ #7A00FF
      - 4.三级坐标:'0,2','0,6','2,4','-2,4'/ #BD7FFF
      - 5.四级坐标:'0,1','0,3','0,5','0,7','1,2','1,4','1,6','-1,2','-1,4','-1,6','2,3','2,5','-2,3','-2,5','-3,4','3,4'/ #BD7FFF
    - 9.粉色:
      - 1.编号:'8'
      - 2.一级坐标:'4,4'/ #FF00D0
      - 3.二级坐标:'-2,6','2,2','6,-2'/  #FFCFD0
      - 4.四级坐标:'-1,7','-1,5','-3,5','3,3','3,1','1,1','1,3','7,-1','5,-3','5,-1'/ #FFCFD0
- 3.中心编号文本颜色:
  - 1.颜色: #ffffff
