# 矩阵系统

## 初始提示词

- 1.阅读‘前端技术栈.md’并用于构建代码
- 2.阅读‘三级容器(矩阵系统布局)路径.md’并用于创建文件
- 3.严格执行‘三级容器(矩阵系统布局).md’中的指示
- 4.禁止提前阅读除提示词描述以外的文档
- 5.仅完成该文本提供的逻辑流程，禁止补全
- 6.检测代码功能是否正常运行
- 7.更新.gitignore文档

## 优化提示词

请按照以下步骤严格执行，不要跳过或添加额外步骤：

1. **阅读技术栈文档**：首先阅读项目根目录下的'前端技术栈.md'文件，理解项目使用的前端技术栈和架构要求，这些信息将用于后续的代码构建工作。
2. **阅读矩阵系统文档**：接着阅读'三级容器(矩阵系统布局).md'文件，理解文件结构和组织方式，这将指导新文件的创建位置和命名规范。
3. **严格遵循矩阵系统指示**：完全按照'三级容器(矩阵系统).md'文档中的具体指示执行，包括文件结构、命名规范、组织方式等所有要求。
4. **限制文档阅读范围**：在执行过程中，除了上述两个指定的提示词文档外，禁止主动阅读项目中的其他文档文件，除非在执行过程中遇到具体的技术问题需要查阅相关代码。
5. **严格按流程执行**：仅完成本指令明确提供的逻辑流程和步骤，不要根据个人判断补充额外的功能或步骤。
6. **代码功能验证**：完成代码编写后，检查代码的语法正确性和基本功能是否能够正常运行，确保没有明显的错误。
7. **更新Git忽略文件**：最后更新项目根目录下的.gitignore文件，确保新增的文件和目录按照项目规范进行版本控制管理。

注意：请严格按照1-7的顺序执行，每完成一步后再进行下一步。
