'use client'

// 坐标接口
export interface Coordinate {
  x: number;
  y: number;
}

/**
 * 计算每个组件的坐标（以中心组件为原点 '0,0'）
 * @param gridRow 网格行数（1-33）
 * @param gridCol 网格列数（1-33）
 * @returns 坐标对象 {x, y}
 */
export const calculateCoordinate = (gridRow: number, gridCol: number): Coordinate => {
  // 33x33网格的中心点是第17行第17列（索引从1开始）
  const centerRow = 17;
  const centerCol = 17;
  
  // 计算相对于中心点的坐标
  // x轴：列方向，向右为正
  const x = gridCol - centerCol;
  // y轴：行方向，向上为正（所以需要反转）
  const y = centerRow - gridRow;
  
  return { x, y };
};

/**
 * 检查坐标是否在33x33范围内
 * @param coordinate 坐标对象
 * @returns 是否在范围内
 */
export const isCoordinateInRange = (coordinate: Coordinate): boolean => {
  // 33x33网格，中心为(0,0)，范围是-16到16
  return coordinate.x >= -16 && coordinate.x <= 16 && 
         coordinate.y >= -16 && coordinate.y <= 16;
};

/**
 * 将坐标转换为字符串格式
 * @param coordinate 坐标对象
 * @returns 坐标字符串，如 "0,0", "1,-1"
 */
export const coordinateToString = (coordinate: Coordinate): string => {
  return `${coordinate.x},${coordinate.y}`;
};
