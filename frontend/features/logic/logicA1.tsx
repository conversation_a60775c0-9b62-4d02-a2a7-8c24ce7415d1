'use client'

import React from 'react';
import FeatureA1Layout from './featureA1_layout';

interface LogicA1Props {
  containerWidth?: string;
  containerHeight?: string;
}

/**
 * 功能容器交互组件
 * 负责管理featureA1组件的交互逻辑
 */
const LogicA1: React.FC<LogicA1Props> = ({ 
  containerWidth = '99%', 
  containerHeight = '99%' 
}) => {
  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <FeatureA1Layout 
        containerWidth={containerWidth}
        containerHeight={containerHeight}
      />
    </div>
  );
};

export default LogicA1;
