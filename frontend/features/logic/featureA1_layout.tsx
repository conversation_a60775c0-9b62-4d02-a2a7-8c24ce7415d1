'use client'

import React from 'react';
import FeatureA1 from '../feature/featureA1';

interface FeatureA1LayoutProps {
  containerWidth?: string;
  containerHeight?: string;
}

const FeatureA1Layout: React.FC<FeatureA1LayoutProps> = ({ 
  containerWidth = '99%', 
  containerHeight = '99%' 
}) => {
  // 网格行数：33
  const gridRows = 33;
  // 网格列数：33
  const gridCols = 33;
  
  // 生成网格项
  const renderGridItems = () => {
    const items = [];
    for (let row = 1; row <= gridRows; row++) {
      for (let col = 1; col <= gridCols; col++) {
        items.push(
          <FeatureA1 
            key={`${row}-${col}`}
            gridRow={row}
            gridCol={col}
          />
        );
      }
    }
    return items;
  };

  return (
    <div
      style={{
        // 容器宽度：componetA1容器宽度99%
        width: containerWidth,
        // 容器高度：componetA1容器高度99%
        height: containerHeight,
        // 排列方式：网格排列
        display: 'grid',
        // 网格模板列：33列
        gridTemplateColumns: `repeat(${gridCols}, 1fr)`,
        // 网格模板行：33行
        gridTemplateRows: `repeat(${gridRows}, 1fr)`,
        // 组件间隔：5px
        gap: '5px',
        // 自动布局系统
        boxSizing: 'border-box',
        padding: '5px',
      }}
    >
      {renderGridItems()}
    </div>
  );
};

export default FeatureA1Layout;
