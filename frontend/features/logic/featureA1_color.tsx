'use client'

import { Coordinate, coordinateToString } from './featureA1_coordinate';

// A组常量对象(group_a)管理颜色坐标
const group_a = {
  // 黑色 - 中心编号:'A'
  black: {
    center: { coord: '0,0', color: '#1E1E1E', number: 'A' }
  },
  
  // 红色 - 编号:'1'
  red: {
    level1: [{ coord: '8,0', color: '#D00000' }],
    level2: [{ coord: '4,0', color: '#D00000' }],
    level3: [
      { coord: '2,0', color: '#FF9190' },
      { coord: '6,0', color: '#FF9190' },
      { coord: '4,2', color: '#FF9190' },
      { coord: '4,-2', color: '#FF9190' }
    ],
    level4: [
      { coord: '1,0', color: '#FF9190' },
      { coord: '3,0', color: '#FF9190' },
      { coord: '5,0', color: '#FF9190' },
      { coord: '7,0', color: '#FF9190' },
      { coord: '2,1', color: '#FF9190' },
      { coord: '2,-1', color: '#FF9190' },
      { coord: '3,2', color: '#FF9190' },
      { coord: '3,-2', color: '#FF9190' },
      { coord: '4,1', color: '#FF9190' },
      { coord: '4,3', color: '#FF9190' },
      { coord: '4,-1', color: '#FF9190' },
      { coord: '4,-3', color: '#FF9190' },
      { coord: '6,1', color: '#FF9190' },
      { coord: '6,-1', color: '#FF9190' },
      { coord: '5,2', color: '#FF9190' },
      { coord: '5,-2', color: '#FF9190' }
    ],
    number: '1'
  },
  
  // 橙色 - 编号:'2'
  orange: {
    level1: [{ coord: '4,-4', color: '#FD5D00' }],
    level3: [
      { coord: '6,2', color: '#FFD500' },
      { coord: '2,-2', color: '#FFD500' },
      { coord: '-2,-6', color: '#FFD500' }
    ],
    level4: [
      { coord: '-3,-5', color: '#FFD500' },
      { coord: '-1,-7', color: '#FFD500' },
      { coord: '-1,-5', color: '#FFD500' },
      { coord: '1,-1', color: '#FFD500' },
      { coord: '1,-3', color: '#FFD500' },
      { coord: '3,-3', color: '#FFD500' },
      { coord: '3,-1', color: '#FFD500' },
      { coord: '5,3', color: '#FFD500' },
      { coord: '7,1', color: '#FFD500' },
      { coord: '5,1', color: '#FFD500' }
    ],
    number: '2'
  },
  
  // 黄色 - 编号:'3'
  yellow: {
    level1: [{ coord: '0,-8', color: '#B4B100' }],
    level2: [{ coord: '0,-4', color: '#B4B100' }],
    level3: [
      { coord: '0,-2', color: '#FFD500' },
      { coord: '0,-6', color: '#FFD500' },
      { coord: '2,-4', color: '#FFD500' },
      { coord: '-2,-4', color: '#FFD500' }
    ],
    level4: [
      { coord: '0,-1', color: '#FFD500' },
      { coord: '-1,-2', color: '#FFD500' },
      { coord: '0,-3', color: '#FFD500' },
      { coord: '1,-2', color: '#FFD500' },
      { coord: '-2,-3', color: '#FFD500' },
      { coord: '-3,-4', color: '#FFD500' },
      { coord: '-2,-5', color: '#FFD500' },
      { coord: '-1,-4', color: '#FFD500' },
      { coord: '2,-3', color: '#FFD500' },
      { coord: '1,-4', color: '#FFD500' },
      { coord: '2,-5', color: '#FFD500' },
      { coord: '3,-4', color: '#FFD500' },
      { coord: '0,-5', color: '#FFD500' },
      { coord: '-1,-6', color: '#FFD500' },
      { coord: '0,-7', color: '#FFD500' },
      { coord: '1,-6', color: '#FFD500' }
    ],
    number: '3'
  },
  
  // 绿色 - 编号:'4'
  green: {
    level1: [{ coord: '-4,-4', color: '#009A15' }],
    level2: [
      { coord: '-6,2', color: '#0FFF35' },
      { coord: '-2,-2', color: '#0FFF35' },
      { coord: '2,-6', color: '#0FFF35' }
    ],
    level4: [
      { coord: '-5,3', color: '#0FFF35' },
      { coord: '-7,1', color: '#0FFF35' },
      { coord: '-5,1', color: '#0FFF35' },
      { coord: '-1,-1', color: '#0FFF35' },
      { coord: '-3,-1', color: '#0FFF35' },
      { coord: '-3,-3', color: '#0FFF35' },
      { coord: '-1,-3', color: '#0FFF35' },
      { coord: '3,-5', color: '#0FFF35' },
      { coord: '1,-5', color: '#0FFF35' },
      { coord: '1,-7', color: '#0FFF35' }
    ],
    number: '4'
  }
};


  // 青色 - 编号:'5'
  cyan: {
    level1: [{ coord: '-8,0', color: '#00BDBD' }],
    level2: [{ coord: '-4,0', color: '#00BDBD' }],
    level3: [
      { coord: '-2,0', color: '#90FFFF' },
      { coord: '-6,0', color: '#90FFFF' },
      { coord: '-4,2', color: '#90FFFF' },
      { coord: '-4,-2', color: '#90FFFF' }
    ],
    level4: [
      { coord: '-7,0', color: '#90FFFF' },
      { coord: '-5,0', color: '#90FFFF' },
      { coord: '-3,0', color: '#90FFFF' },
      { coord: '-1,0', color: '#90FFFF' },
      { coord: '-6,1', color: '#90FFFF' },
      { coord: '-6,-1', color: '#90FFFF' },
      { coord: '-5,2', color: '#90FFFF' },
      { coord: '-5,-2', color: '#90FFFF' },
      { coord: '-4,1', color: '#90FFFF' },
      { coord: '-4,3', color: '#90FFFF' },
      { coord: '-4,-1', color: '#90FFFF' },
      { coord: '-4,-3', color: '#90FFFF' },
      { coord: '-2,1', color: '#90FFFF' },
      { coord: '-2,-1', color: '#90FFFF' },
      { coord: '-3,2', color: '#90FFFF' },
      { coord: '-3,-2', color: '#90FFFF' }
    ],
    number: '5'
  },

  // 蓝色 - 编号:'6'
  blue: {
    level1: [{ coord: '-4,4', color: '#0D00FF' }],
    level2: [
      { coord: '2,6', color: '#0FD7FF' },
      { coord: '-2,2', color: '#0FD7FF' },
      { coord: '-6,-2', color: '#0FD7FF' }
    ],
    level4: [
      { coord: '1,7', color: '#0FD7FF' },
      { coord: '3,5', color: '#0FD7FF' },
      { coord: '1,5', color: '#0FD7FF' },
      { coord: '-3,3', color: '#0FD7FF' },
      { coord: '-1,3', color: '#0FD7FF' },
      { coord: '-1,1', color: '#0FD7FF' },
      { coord: '-3,1', color: '#0FD7FF' },
      { coord: '-7,-1', color: '#0FD7FF' },
      { coord: '-5,-1', color: '#0FD7FF' },
      { coord: '-5,-3', color: '#0FD7FF' }
    ],
    number: '6'
  },

  // 紫色 - 编号:'7'
  purple: {
    level1: [{ coord: '0,8', color: '#7A00FF' }],
    level2: [{ coord: '0,4', color: '#7A00FF' }],
    level3: [
      { coord: '0,2', color: '#BD7FFF' },
      { coord: '0,6', color: '#BD7FFF' },
      { coord: '2,4', color: '#BD7FFF' },
      { coord: '-2,4', color: '#BD7FFF' }
    ],
    level4: [
      { coord: '0,1', color: '#BD7FFF' },
      { coord: '0,3', color: '#BD7FFF' },
      { coord: '0,5', color: '#BD7FFF' },
      { coord: '0,7', color: '#BD7FFF' },
      { coord: '1,2', color: '#BD7FFF' },
      { coord: '1,4', color: '#BD7FFF' },
      { coord: '1,6', color: '#BD7FFF' },
      { coord: '-1,2', color: '#BD7FFF' },
      { coord: '-1,4', color: '#BD7FFF' },
      { coord: '-1,6', color: '#BD7FFF' },
      { coord: '2,3', color: '#BD7FFF' },
      { coord: '2,5', color: '#BD7FFF' },
      { coord: '-2,3', color: '#BD7FFF' },
      { coord: '-2,5', color: '#BD7FFF' },
      { coord: '-3,4', color: '#BD7FFF' },
      { coord: '3,4', color: '#BD7FFF' }
    ],
    number: '7'
  },

  // 粉色 - 编号:'8'
  pink: {
    level1: [{ coord: '4,4', color: '#FF00D0' }],
    level2: [
      { coord: '-2,6', color: '#FFCFD0' },
      { coord: '2,2', color: '#FFCFD0' },
      { coord: '6,-2', color: '#FFCFD0' }
    ],
    level4: [
      { coord: '-1,7', color: '#FFCFD0' },
      { coord: '-1,5', color: '#FFCFD0' },
      { coord: '-3,5', color: '#FFCFD0' },
      { coord: '3,3', color: '#FFCFD0' },
      { coord: '3,1', color: '#FFCFD0' },
      { coord: '1,1', color: '#FFCFD0' },
      { coord: '1,3', color: '#FFCFD0' },
      { coord: '7,-1', color: '#FFCFD0' },
      { coord: '5,-3', color: '#FFCFD0' },
      { coord: '5,-1', color: '#FFCFD0' }
    ],
    number: '8'
  }
};

/**
 * 获取指定坐标的颜色和编号信息
 * @param coordinate 坐标对象
 * @returns 颜色信息对象或null
 */
const getCoordinateInfo = (coordinate: Coordinate) => {
  const coordStr = coordinateToString(coordinate);

  // 检查黑色中心点
  if (coordStr === group_a.black.center.coord) {
    return {
      color: group_a.black.center.color,
      number: group_a.black.center.number,
      textColor: '#ffffff' // 中心编号文本颜色
    };
  }

  // 检查所有颜色组
  const colorGroups = [
    group_a.red, group_a.orange, group_a.yellow, group_a.green,
    group_a.cyan, group_a.blue, group_a.purple, group_a.pink
  ];

  for (const group of colorGroups) {
    // 检查各个级别
    const levels = [group.level1, group.level2, group.level3, group.level4].filter(Boolean);

    for (const level of levels) {
      const found = level?.find(item => item.coord === coordStr);
      if (found) {
        return {
          color: found.color,
          number: group.number,
          textColor: '#242424'
        };
      }
    }
  }

  return null;
};

/**
 * 获取组件的背景颜色
 * @param coordinate 坐标对象
 * @param modeButtonActive 模式按键状态
 * @returns 背景颜色字符串
 */
export const getCoordinateColor = (coordinate: Coordinate, modeButtonActive: boolean): string => {
  // 监听模式按键状态
  if (modeButtonActive) {
    // 状态为true时，组件底色为白色
    return '#ffffff';
  } else {
    // 状态为false时，使用坐标底色
    const info = getCoordinateInfo(coordinate);
    return info ? info.color : '#ffffff'; // 默认白色
  }
};

/**
 * 获取组件的编号文本
 * @param coordinate 坐标对象
 * @param modeButtonActive 模式按键状态
 * @returns 编号文本或空字符串
 */
export const getCoordinateNumber = (coordinate: Coordinate, modeButtonActive: boolean): string => {
  if (modeButtonActive) {
    return ''; // 模式状态下不显示编号
  }

  const info = getCoordinateInfo(coordinate);
  return info ? info.number : '';
};

/**
 * 获取文本颜色
 * @param coordinate 坐标对象
 * @param modeButtonActive 模式按键状态
 * @returns 文本颜色
 */
export const getTextColor = (coordinate: Coordinate, modeButtonActive: boolean): string => {
  if (modeButtonActive) {
    return '#242424'; // 模式状态下使用默认文本颜色
  }

  const info = getCoordinateInfo(coordinate);
  return info ? info.textColor : '#242424';
};
