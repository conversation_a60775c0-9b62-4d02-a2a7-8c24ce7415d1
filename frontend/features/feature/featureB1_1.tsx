'use client';

import React from 'react';
import { useMatrixStore } from '../../Store/store';
import SecondaryButton from '../../ButtonCodes/secondary/SecondaryButton/SecondaryButton';

const FeatureB1_1: React.FC = () => {
  const { 
    coordinateButtonActive, 
    toggleCoordinateButton, 
    triggerInitialization 
  } = useMatrixStore();

  return (
    <div style={{
      position: 'relative',
      height: '15%',
      width: '94%',
      backgroundColor: '#bebebe',
      overflow: 'hidden',
      top: '6%',
    }}>
      {/* 矩阵文本 */}
      <div style={{
        position: 'absolute',
        top: '20%',
        left: '1%',
        fontSize: '25px',
        color: '#242424',
      }}>
        矩阵
      </div>

      {/* 初始化按键 */}
      <SecondaryButton
        text="初始化"
        style={{
          position: 'absolute',
          top: '55%',
          left: '4%',
          height: '35%',
          width: '44%',
          fontSize: 'clamp(12px, 2vw, 20px)', // 自适应字体大小
        }}
        onClick={triggerInitialization}
        mode="instant"
      />

      {/* 坐标按键 */}
      <SecondaryButton
        text="坐标"
        style={{
          position: 'absolute',
          top: '55%',
          right: '4%',
          height: '35%',
          width: '44%',
          fontSize: 'clamp(12px, 2vw, 20px)', // 自适应字体大小
        }}
        isActive={coordinateButtonActive}
        onClick={toggleCoordinateButton}
        mode="toggle"
      />
    </div>
  );
};

export default FeatureB1_1;
