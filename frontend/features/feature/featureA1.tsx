'use client'

import React from 'react';
import { useMatrixStore, useButtonStore } from '../../Store/store';
import { getCoordinateColor, getCoordinateNumber, getTextColor } from '../logic/featureA1_color';
import { calculateCoordinate } from '../logic/featureA1_coordinate';

interface FeatureA1Props {
  gridRow: number;
  gridCol: number;
}

const FeatureA1: React.FC<FeatureA1Props> = ({ gridRow, gridCol }) => {
  const { coordinateButtonActive } = useMatrixStore();
  const { modeButtonActive } = useButtonStore();
  
  // 计算坐标（以中心为原点）
  const coordinate = calculateCoordinate(gridRow, gridCol);
  
  // 获取背景颜色
  const backgroundColor = getCoordinateColor(coordinate, modeButtonActive);

  // 获取编号文本
  const numberText = getCoordinateNumber(coordinate, modeButtonActive);

  // 获取坐标文本
  const coordinateText = coordinateButtonActive ? `${coordinate.x},${coordinate.y}` : '';

  // 获取文本颜色
  const textColor = getTextColor(coordinate, modeButtonActive);

  // 显示的文本：优先显示坐标，其次显示编号
  const displayText = coordinateText || numberText;
  
  return (
    <div
      style={{
        // 组件形状：方形
        aspectRatio: '1',
        // 组件圆角：5px
        borderRadius: '5px',
        // 展示方式：弹性布局
        display: 'flex',
        // 弹性方向：垂直
        flexDirection: 'column',
        // 对齐方式：水平，垂直居中
        justifyContent: 'center',
        alignItems: 'center',
        // 溢出处理：隐藏
        overflow: 'hidden',
        // 组件定位：相对定位
        position: 'relative',
        // 鼠标指针：手型
        cursor: 'pointer',
        // 背景颜色
        backgroundColor,
        // 字体大小：8px
        fontSize: '8px',
        // 字体颜色：动态获取
        color: textColor,
        // 字体对齐：居中
        textAlign: 'center',
        // 组件尺寸：自动确定
        width: '100%',
        height: '100%',
      }}
    >
      {displayText}
    </div>
  );
};

export default FeatureA1;
