import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import FeatureA1 from '../../../frontend/features/feature/featureA1';
import { calculateCoordinate } from '../../../frontend/features/logic/featureA1_coordinate';
import { getCoordinateColor } from '../../../frontend/features/logic/featureA1_color';

// Mock Zustand store
jest.mock('../../../frontend/Store/store', () => ({
  useMatrixStore: () => ({
    coordinateButtonActive: false,
  }),
  useButtonStore: () => ({
    modeButtonActive: true,
  }),
}));

describe('FeatureA1 Component', () => {
  test('renders without crashing', () => {
    render(<FeatureA1 gridRow={17} gridCol={17} />);
  });

  test('applies correct styles', () => {
    const { container } = render(<FeatureA1 gridRow={17} gridCol={17} />);
    const element = container.firstChild as HTMLElement;
    
    expect(element).toHaveStyle({
      borderRadius: '5px',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      overflow: 'hidden',
      position: 'relative',
      cursor: 'pointer',
      fontSize: '8px',
      textAlign: 'center',
      width: '100%',
      height: '100%',
    });
  });
});

describe('Coordinate Calculation', () => {
  test('calculates center coordinate correctly', () => {
    const coordinate = calculateCoordinate(17, 17);
    expect(coordinate).toEqual({ x: 0, y: 0 });
  });

  test('calculates positive coordinates correctly', () => {
    const coordinate = calculateCoordinate(16, 18);
    expect(coordinate).toEqual({ x: 1, y: 1 });
  });

  test('calculates negative coordinates correctly', () => {
    const coordinate = calculateCoordinate(18, 16);
    expect(coordinate).toEqual({ x: -1, y: -1 });
  });
});

describe('Color System', () => {
  test('returns white color when mode button is active', () => {
    const coordinate = { x: 0, y: 0 };
    const color = getCoordinateColor(coordinate, true);
    expect(color).toBe('#ffffff');
  });

  test('returns center color when mode button is inactive and coordinate is center', () => {
    const coordinate = { x: 0, y: 0 };
    const color = getCoordinateColor(coordinate, false);
    expect(color).toBe('#1E1E1E');
  });

  test('returns default white for unknown coordinates when mode button is inactive', () => {
    const coordinate = { x: 100, y: 100 };
    const color = getCoordinateColor(coordinate, false);
    expect(color).toBe('#ffffff');
  });
});
