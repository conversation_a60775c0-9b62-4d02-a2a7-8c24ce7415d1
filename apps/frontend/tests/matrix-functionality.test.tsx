import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import FeatureB1_1 from '../../../frontend/features/feature/featureB1_1';
import { useMatrixStore } from '../../../frontend/Store/store';

// 模拟 Zustand store
jest.mock('../../../frontend/Store/store', () => ({
  useMatrixStore: jest.fn()
}));

describe('矩阵功能面板测试', () => {
  const mockToggleCoordinateButton = jest.fn();
  const mockTriggerInitialization = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useMatrixStore as unknown as jest.Mock).mockReturnValue({
      coordinateButtonActive: false,
      toggleCoordinateButton: mockToggleCoordinateButton,
      triggerInitialization: mockTriggerInitialization
    });
  });

  test('应该渲染矩阵文本', () => {
    render(<FeatureB1_1 />);
    expect(screen.getByText('矩阵')).toBeInTheDocument();
  });

  test('应该渲染初始化按键', () => {
    render(<FeatureB1_1 />);
    expect(screen.getByText('初始化')).toBeInTheDocument();
  });

  test('应该渲染坐标按键', () => {
    render(<FeatureB1_1 />);
    expect(screen.getByText('坐标')).toBeInTheDocument();
  });

  test('点击初始化按键应该触发初始化事件', () => {
    render(<FeatureB1_1 />);
    const initButton = screen.getByText('初始化');
    fireEvent.click(initButton);
    expect(mockTriggerInitialization).toHaveBeenCalledTimes(1);
  });

  test('点击坐标按键应该切换状态', () => {
    render(<FeatureB1_1 />);
    const coordButton = screen.getByText('坐标');
    fireEvent.click(coordButton);
    expect(mockToggleCoordinateButton).toHaveBeenCalledTimes(1);
  });
});

export default {};
