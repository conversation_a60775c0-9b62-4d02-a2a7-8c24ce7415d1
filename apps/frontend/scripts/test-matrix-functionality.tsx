import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import FeatureB1_1 from '../../../frontend/features/feature/featureB1_1';
import { useMatrixStore } from '../../../frontend/Store/store';

// 模拟 Zustand store
jest.mock('../../../frontend/Store/store', () => ({
  useMatrixStore: jest.fn()
}));

describe('矩阵功能面板测试', () => {
  const mockToggleCoordinateButton = jest.fn();
  const mockTriggerInitialization = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useMatrixStore as unknown as jest.Mock).mockReturnValue({
      coordinateButtonActive: false,
      toggleCoordinateButton: mockToggleCoordinateButton,
      triggerInitialization: mockTriggerInitialization
    });
  });

  test('应该渲染矩阵文本', () => {
    render(<FeatureB1_1 />);
    expect(screen.getByText('矩阵')).toBeInTheDocument();
  });

  test('应该渲染初始化按键', () => {
    render(<FeatureB1_1 />);
    expect(screen.getByText('初始化')).toBeInTheDocument();
  });

  test('应该渲染坐标按键', () => {
    render(<FeatureB1_1 />);
    expect(screen.getByText('坐标')).toBeInTheDocument();
  });

  test('点击初始化按键应该触发初始化事件', () => {
    render(<FeatureB1_1 />);
    const initButton = screen.getByText('初始化');
    fireEvent.click(initButton);
    expect(mockTriggerInitialization).toHaveBeenCalledTimes(1);
  });

  test('点击坐标按键应该切换状态', () => {
    render(<FeatureB1_1 />);
    const coordButton = screen.getByText('坐标');
    fireEvent.click(coordButton);
    expect(mockToggleCoordinateButton).toHaveBeenCalledTimes(1);
  });

  test('坐标按键应该显示正确的激活状态', () => {
    (useMatrixStore as unknown as jest.Mock).mockReturnValue({
      coordinateButtonActive: true,
      toggleCoordinateButton: mockToggleCoordinateButton,
      triggerInitialization: mockTriggerInitialization
    });

    render(<FeatureB1_1 />);
    const coordButton = screen.getByText('坐标');
    // 这里可以添加更多关于激活状态的断言
    expect(coordButton).toBeInTheDocument();
  });
});

// 状态管理测试
describe('矩阵状态管理测试', () => {
  test('初始化应该重置坐标按键状态并更新时间戳', () => {
    // 这里需要实际的状态管理测试
    // 由于我们使用的是 Zustand，需要特殊的测试设置
    console.log('状态管理测试需要在实际环境中运行');
  });
});

export default {};
